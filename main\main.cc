#include <esp_log.h>
#include <esp_err.h>
#include <nvs.h>
#include <nvs_flash.h>
#include <driver/gpio.h>
#include "link_protocol.h"

static const char* TAG = "MAIN";

uint8_t ble_addr_type;



void app_main(void)
{
    // Initialize the default event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Initialize NVS flash for WiFi configuration
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_LOGW(TAG, "Erasing NVS flash to fix corruption");
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    nimble_port_init();                             // 2 - Initialize the host and controller stack
    ble_svc_gap_device_name_set("BLE-Scan-Client"); // 3 - Set device name characteristic
    ble_svc_gap_init();                             // 3 - Initialize GAP service
    ble_hs_cfg.sync_cb = ble_app_on_sync;           // 4 - Set application
    nimble_port_freertos_init(host_task);           // 5 - Set infinite task
}
